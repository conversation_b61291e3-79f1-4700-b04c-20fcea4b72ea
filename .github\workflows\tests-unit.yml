name: Unit tests

on: [push, pull_request]

jobs:
  unit-tests:
    name: Unit tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.5, 3.6, 3.7, 3.8, 3.9]
    steps:
    - name: Checkout repo
      uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade -r pip-requirements.txt
        pip install tox tox-gh-actions coveralls
        bash ryu/tests/integrated/common/install_docker_test_pkg_for_github_actions.sh
    - name: Test with tox
      run: NOSE_VERBOSE=0 tox
